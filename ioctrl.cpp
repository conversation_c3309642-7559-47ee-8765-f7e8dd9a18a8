//输入输出指令
#include "ioctrl.h"
#include <string.h>
#include <time.h>
#include <mutex>
#include <map>
#include <sysSemaphore.h>
#include "adconfig.h"
#include "asiosocket.h"
#include "cmdcode.h"
//#include <qdebug.h>
#include "ADOSType.h"
#include <atomic>
#include <vector>
#include "motion_types.h" // 直接包含SpeedIOPoint结构体定义
#include "ADMotionPackage.h"
#include "CardData.h"
#include "motion_error_codes.h"  // 使用新的错误码头文件
#include "logger_proxy.h"
#include "logger_internal.h"
#include <cstdio>  // For snprintf

#ifdef _WIN32
#include <sysinfoapi.h>
#include <windows.h>
#else
#include <unistd.h>
#include <sys/time.h>
#endif

namespace SYSTEMOPT
{
inline unsigned __int64 GetTickCount()
{
#ifdef _WIN32
    return ::GetTickCount();
#else
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (ts.tv_sec * 1000 + ts.tv_nsec / 1000000);
#endif
}
} // namespace SYSTEMOPT

unsigned short GetIndex(TADMotionConn* handle)
{ // in thread
    TCardData* pCard = handle->CardData;
    int        n     = ++pCard->pctrl->g_postindex;
    if (n >= 1023)
    {
        n = 1024 - pCard->pctrl->g_postindex;
        if (n < 0)
            n = 0;
        pCard->pctrl->g_postindex = n;
    }
    return n;
}

void CommandUninitial(TADMotionConn* handle, unsigned short tick)
{
    // gCommandBuffer.erase(tick);
}

void CommandInitial(TADMotionConn* handle, unsigned short tick, unsigned short cmd)
{
    short           i;
    TCardData*      pCard  = handle->CardData;
    TCommandBuffer& cmdbuf = pCard->gCommandBuffer[tick];
    cmdbuf.data[0]         = cmd;
    cmdbuf.dataCount         = 1;
    for (i = 1; i < COMMAND_DATA_LEN; i++)
    {
        cmdbuf.data[i] = 0;
    }

    for (i = 0; i < COMMAND_DATA_LEN; i++)
    {
        cmdbuf.result[i] = 0;
    }
    cmdbuf.resultIndex = 1;
}

void Add16ToBuff(TADMotionConn* handle, unsigned short tick, short data)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].dataCount < COMMAND_DATA_LEN)
    {
        pCard->gCommandBuffer[tick].data[pCard->gCommandBuffer[tick].dataCount] = data;
        pCard->gCommandBuffer[tick].dataCount += 1;
    }
}
void Add32ToBuff(TADMotionConn* handle, unsigned short tick, int32_t data)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].dataCount + 1 < COMMAND_DATA_LEN)
    {
        int32_t* p = (int32_t*)&pCard->gCommandBuffer[tick].data[pCard->gCommandBuffer[tick].dataCount];
        memcpy(p, &data, sizeof(int32_t));
        pCard->gCommandBuffer[tick].dataCount += 2;
    }
}
void Get32FromBuff(TADMotionConn* handle, unsigned short tick, int32_t* pData)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].resultIndex + 1 < pCard->gCommandBuffer[tick].resultCount)
    {
        TCommandBuffer &cmdbuf = pCard->gCommandBuffer[tick];
        int32_t* p = (int32_t*)&cmdbuf.result[cmdbuf.resultIndex];
        //            *pData     = *p;
        memcpy(pData, p, sizeof(int32_t));
        cmdbuf.resultIndex += 2;
    }
}

void Get32Fix24FromBuff(TADMotionConn* handle, unsigned short tick, double* pData)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].resultIndex + 1 < pCard->gCommandBuffer[tick].resultCount)
    {
        int32_t* p = (int32_t*)&pCard->gCommandBuffer[tick].result[pCard->gCommandBuffer[tick].resultIndex];
        *pData     = (*p) / (double)(1 << 24);
        pCard->gCommandBuffer[tick].resultIndex += 2;
    }
}
void Get64Fix16FromBuff(TADMotionConn* handle, unsigned short tick, double* pData)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].resultIndex + 3 < pCard->gCommandBuffer[tick].resultCount)
    {
        __int64* p = (__int64*)&pCard->gCommandBuffer[tick].result[pCard->gCommandBuffer[tick].resultIndex];
        memcpy(pData, p, sizeof(__int64));
        pCard->gCommandBuffer[tick].resultIndex += 4;
    }
}
void Get32Fix16FromBuff(TADMotionConn* handle, unsigned short tick, double* pData)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].resultIndex + 1 < pCard->gCommandBuffer[tick].resultCount)
    {
        int32_t* p = (int32_t*)&pCard->gCommandBuffer[tick].result[pCard->gCommandBuffer[tick].resultIndex];
        *pData     = (*p) / 65536.0;
        pCard->gCommandBuffer[tick].resultIndex += 2;
    }
}
void Add32Fix16ToBuff(TADMotionConn* handle, unsigned short tick, double data)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].dataCount + 1 < COMMAND_DATA_LEN)
    {
        int32_t  n = data;
        int32_t* p = (int32_t*)&pCard->gCommandBuffer[tick].data[pCard->gCommandBuffer[tick].dataCount];
        //            *p         = (int32_t)(data);
        memcpy(p, &n, sizeof(int32_t));
        pCard->gCommandBuffer[tick].dataCount += 2;
    }
}
void Get16FromBuff(TADMotionConn* handle, unsigned short tick, short* pData)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].resultIndex < pCard->gCommandBuffer[tick].resultCount)
    {
        *pData = pCard->gCommandBuffer[tick].result[pCard->gCommandBuffer[tick].resultIndex];
        pCard->gCommandBuffer[tick].resultIndex += 1;
    }
}
void Add64Fix16ToBuff(TADMotionConn* handle, unsigned short tick, double data)
{
    TCardData* pCard = handle->CardData;
    if (pCard->gCommandBuffer[tick].dataCount + 3 < COMMAND_DATA_LEN)
    {
        __int64           n = data;
        unsigned __int64* p = (unsigned __int64*)&pCard->gCommandBuffer[tick].data[pCard->gCommandBuffer[tick].dataCount];
        memcpy(p, &n, sizeof(__int64));
        pCard->gCommandBuffer[tick].dataCount += 4;
    }
}

short MC_Close(TADMotionConn* handle)
{
    handle->Close();
    return CMD_SUCCESS;
}

short MC_Open(TADMotionConn* handle, const char* ip, int port)
{
    if (!ip) {
        return CMD_API_ERROR_POINTER;
    }

    bool ok = handle->Connection(ip, port);

    if (ok) {
        return CMD_SUCCESS;
    } else {
        return CMD_API_ERROR_OPEN;
    }
}

short SendToPci(TADMotionConn* handle)
{
    if (handle == nullptr || !handle->isOpen())
        return CONNECTIONERROR;

    //清空缓冲区
    TCardData* pCard  = handle->CardData;
    char*      ppci   = (char*)&pCard->gPci;
    int        pcilen = 2 * (pCard->gPci.ArrayCount + 4);
    SemReset(&pCard->pctrl->sema);

    handle->write(ppci, pcilen);

    int flag = SemWait(&pCard->pctrl->sema, 3000);
    if (flag != 0)
        return TIMEOUT;
    return 0;
}

short SendCommand(TADMotionConn* handle, unsigned short tick)
{
    TCardData*                    pCard = handle->CardData;
    std::lock_guard< std::mutex > Lock(pCard->pctrl->socketmutex);

    short sendok = 0;
    short i;

    memset(&pCard->gPci, 0, COMMAND_DATA_LEN);

    pCard->gPci.tick    = tick;
    pCard->gPci.ArrayCount = pCard->gCommandBuffer[tick].dataCount;
    for (i = 0; i < pCard->gCommandBuffer[tick].dataCount; i++)
        pCard->gPci.data[i] = pCard->gCommandBuffer[tick].data[i];

    sendok = SendToPci(handle); //sendok == 0 指令发出成功
    if (CMD_SUCCESS != sendok)
    {
        return sendok;
    }
   //取返回值
    pCard->gCommandBuffer[tick].resultCount = pCard->gPci.ArrayCount;
    for (i = 0; i < pCard->gCommandBuffer[tick].resultCount; i++)
    {
        pCard->gCommandBuffer[tick].result[i] = pCard->gPci.data[i];
    }
    int ErrorNo = 0 ;
    if(pCard->gPci.ArrayCount > 0)
       ErrorNo =  pCard->gPci.data[0];

    if(ErrorNo == 0 || ErrorNo == 1)
    {
      ErrorNo = CMD_SUCCESS ;
    }
    else //出错了
    {
        //  std::wstring errcode = MotionGetErrorStr(ErrorNo);
    }
    return ErrorNo;
}


//控制设备OUTPUT电平
short MC_SetDeviceOutput(TADMotionConn* handle, int32_t* deviceOutput)
{
    g_Log(handle, LOG_INFO, "MC_SetDeviceOutput: 设置输出电平");

    if (!deviceOutput) {
        return LogAndReturnError(handle, CMD_API_ERROR_POINTER, "MC_SetDeviceOutput",
                                "输出参数指针为空");
    }

    g_Log(handle, LOG_DEBUG, "MC_SetDeviceOutput: 输出值=0x%08X", *deviceOutput);

    TCardData* pCard = handle->CardData;
    pCard->gCardIO.Output[0] = *deviceOutput;

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, SETDeviceOUTPUT);
    Add32ToBuff(handle, tick, *deviceOutput);
    short ret = SendCommand(handle, tick);
    if (ret != CMD_SUCCESS) {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "输出值=0x%08X", *deviceOutput);
        return LogSendCommandError(handle, ret, "MC_SetDeviceOutput", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetDeviceOutput: 成功设置输出电平=0x%08X", *deviceOutput);
    return ret;
}
//返回设备OUTPUT电平
short MC_GetDeviceOutput(TADMotionConn* handle, int32_t* deviceOutput)
{
//    g_Log(handle, LOG_DEBUG, "MC_GetDeviceOutput: 获取输出电平");

//    if (!deviceOutput) {
//        return LogAndReturnError(handle, CMD_API_ERROR_POINTER, "MC_GetDeviceOutput",
//                                "输出参数指针为空");
//    }

    TCardData* pCard = handle->CardData;
    *deviceOutput    = pCard->gCardIO.Output[0]+(pCard->gCardIO.Output[1]<<16);

    //INTERNAL_LOG(handle, "MC_GetDeviceOutput: 输出电平=0x%08X", *deviceOutput);
    return 0;
}

//返回设备INPUT电平
short MC_GetDeviceInput(TADMotionConn* handle, int32_t* deviceInput)
{
//    g_Log(handle, LOG_DEBUG, "MC_GetDeviceInput: 获取输入电平");

//    if (!deviceInput) {
//        return LogAndReturnError(handle, CMD_API_ERROR_POINTER, "MC_GetDeviceInput",
//                                "输入参数指针为空");
//    }

    TCardData* pCard = handle->CardData;
    *deviceInput     = pCard->gCardIO.Input[0] + (pCard->gCardIO.Input[1]<<16);

    //INTERNAL_LOG(handle, "MC_GetDeviceInput: 输入电平=0x%08X", *deviceInput);
    return 0;
}

//设置高速IO参数
short MC_SetSpeedIOParam(TADMotionConn* handle, short io_num, short duty, short period)
{
    g_Log(handle, LOG_INFO, "MC_SetSpeedIOParam: io_num=%d, duty=%d, period=%d", io_num, duty, period);

    if (io_num < 1 || io_num > 2) {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_SetSpeedIOParam",
                                "无效的IO号 %d (有效范围: 1-2)", io_num);
    }

    if (duty < 0 || period <= 0) {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_SetSpeedIOParam",
                                "无效的参数: duty=%d, period=%d", duty, period);
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, GETSpeedIOParam);
    Add16ToBuff(handle, tick, io_num);  // io_num = 1 or 2
    Add16ToBuff(handle, tick, duty);    // 占空比(us)
    Add16ToBuff(handle, tick, period);  // 触发周期(us), if duty==pariod, 常开模式

    short ret = SendCommand(handle, tick);
    if (ret != CMD_SUCCESS) {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "io_num=%d, duty=%d, period=%d", io_num, duty, period);
        return LogSendCommandError(handle, ret, "MC_SetSpeedIOParam", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetSpeedIOParam: 成功设置高速IO参数");
    return ret;
}

//手动控制高速IO电平
short MC_SetSpeedIOState(TADMotionConn* handle, short io_num, short switch_state)
{
    g_Log(handle, LOG_INFO, "MC_SetSpeedIOState: io_num=%d, state=%d", io_num, switch_state);

    if (io_num < 1 || io_num > 2) {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_SetSpeedIOState",
                                "无效的IO号 %d (有效范围: 1-2)", io_num);
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, GETSpeedIOState);
    Add16ToBuff(handle, tick, io_num);      // io_num = 1 or 2
    Add16ToBuff(handle, tick, switch_state);// 0:关 1:开

    short ret = SendCommand(handle, tick);
    if (ret != CMD_SUCCESS) {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "io_num=%d, state=%d", io_num, switch_state);
        return LogSendCommandError(handle, ret, "MC_SetSpeedIOState", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetSpeedIOState: 成功设置高速IO状态");
    return ret;
}

short MC_SetIOPluseEnable(TADMotionConn* handle, short crd,short io_num, short IO_Enable)
{
    g_Log(handle, LOG_INFO, "MC_SetIOPluseEnable: crd=%d, io_num=%d, enable=%d", crd, io_num, IO_Enable);

    if (crd < 0)
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_SetIOPluseEnable",
                                "无效的坐标系号 %d", crd);
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, IOCMD_SetIOPluseEnable);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, io_num);
    Add16ToBuff(handle, tick, IO_Enable);

    short ret = SendCommand(handle, tick);
    if (ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d, io_num=%d, enable=%d", crd, io_num, IO_Enable);
        return LogSendCommandError(handle, ret, "MC_SetIOPluseEnable", commandInfo);
    }

    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetIOPluseEnable: 成功设置IO脉冲使能");
    return ret;
}


short MC_SetIOPluseState(TADMotionConn* handle, short crd,short io_num, short IO_State)
{
    g_Log(handle, LOG_INFO, "MC_SetIOPluseState: crd=%d, io_num=%d, state=%d", crd, io_num, IO_State);

    if (crd < 0)
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_SetIOPluseState",
                                "无效的坐标系号 %d", crd);
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, IOCMD_SetIOPluseState);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, io_num);
    Add16ToBuff(handle, tick, IO_State);

    short ret = SendCommand(handle, tick);
    if (ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d, io_num=%d, state=%d", crd, io_num, IO_State);
        return LogSendCommandError(handle, ret, "MC_SetIOPluseState", commandInfo);
    }

    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetIOPluseState: 成功设置IO脉冲状态");
    return ret;
}

short  MC_SetIOPluseTrigger(TADMotionConn* handle, short crd,short io_num, short IO_Trigger)
{
    if (crd < 0)
    {
        return CMD_API_ERROR_OUT_RANGE;
    }
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, IOCMD_SetIOPluseTrigger);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, io_num);
    Add16ToBuff(handle, tick, IO_Trigger);

    short ret = SendCommand(handle, tick);
    if (ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        return ret;
    }

    CommandUninitial(handle, tick);
    return ret;
}

short  MC_GetFpgaVersion(TADMotionConn *handle,short &version)
{
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, IOCMD_GetFpgaVersion);
    short ret = SendCommand(handle, tick);
    version = 0;
    Get16FromBuff(handle, tick,&version);

    if (ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        return ret;
    }

    CommandUninitial(handle, tick);
    return ret;
}
//-------------------------------------
/// \brief MC_SendSpeedIO_Point
/// \param handle
/// \param crd
/// \param pointNum :点位数量
/// \param p        ：数组
/// \return
///
short MC_SendSpeedIO_Point(TADMotionConn* handle, short crd, short pointNum,SpeedIOPoint p[])
{
    SpeedIOPoint *pIOPoint;

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, SENDSPEEDIOPOINT);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, pointNum);

    for(int i=0;i<pointNum;i++)
    {
        pIOPoint = p+i;
        Add64Fix16ToBuff(handle, tick,pIOPoint->x);
        Add64Fix16ToBuff(handle, tick,pIOPoint->y);
        Add16ToBuff(handle, tick, pIOPoint->isOpen);
        Add16ToBuff(handle, tick, pIOPoint->openStyle);
    }
    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}
///////////////////////////////
/// \brief MC_SpeedIO_Enable
/// \param handle
/// \param crd
/// \param enable ：点胶功能开关
/// \return
///
short MC_SpeedIO_Enable(TADMotionConn* handle, short crd,short enable,short IO_num)
{
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, SENDSPEEDIOEnable);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, enable);
    Add16ToBuff(handle, tick, IO_num);

    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}
short MC_SpeedIO_ClearPoint(TADMotionConn* handle, short crd)
{
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, SENDSPEEDIOClearPoint);
    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
}

short MC_GetAxisStatus(TADMotionConn* handle, short axis, short& Sts)
{
    TCardData* pCard = handle->CardData;

    short crd = axis/2;
    short axisTemp = axis%2;
    std::shared_lock<std::shared_mutex> lock(pCard->pctrl->cardio_mutex);
    Sts = pCard->gCardIO.positionCrd[crd][axisTemp];

    return 0;
}

short MC_DebugModelOption(TADMotionConn* handle, bool status) // TCP/IP自动重连开关
{
    short rtn = 0;

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, CMD_ETHERNET_RESTART_OPEM);
    Add16ToBuff(handle, tick, status);

    rtn = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return rtn;
}
