﻿#ifndef CARDDATA_H
#define CARDDATA_H
#include <vector>
#include <atomic>
#include "card.h"
#include "ioctrl.h"
#include "sysSemaphore.h"
#include <shared_mutex>
#pragma pack(1)
typedef struct
{
    unsigned short flag;
    unsigned short ArrayCount;//data Array Count
    unsigned short checksum;
    unsigned short tick;   //指令标识，发送的tick与返回的tick要一致
    short          data[COMMAND_DATA_LEN];//data[0] = 命令CMD , 实际数据从data[1]开始
} TPci;

typedef struct
{
    short dataCount;//data Array Count
    short data[COMMAND_DATA_LEN];  //上位机要发送的指令数据

    short resultCount;//result Array Count
    short result[COMMAND_DATA_LEN];//运控返回的数据
    short resultIndex;
} TCommandBuffer;
//一张运控卡需要的内部数据_C++
struct TCardData_cplusplus
{
semDATA    sema; //注意：Linux下首地址8字节对齐
std::mutex socketmutex;
std::shared_mutex cardio_mutex;  // 新增：保护gCardIO的读写锁
std::atomic_ushort g_postindex; //报文ID
};
//一张运控卡需要的内部数据_C
typedef struct TCardData
{
    TCardData_cplusplus *pctrl;
    TCardInfo        CardInfo;
    TCardAxisPos     gCardAxisPos;       //一个运控的所有轴的坐标
    TCardIO          gCardIO;            //一个运控的所有的IO
    unsigned __int64 tick_UpdategCardIO; //更新IO的时间点

    short              gResourceTable[RES_TYPE_MAX];
    TCommandBuffer     gCommandBuffer[1024];
    TCrdApiPrm         gCrdApiPrm[MAX_CRD]; //一张卡支持的最多坐标系
    TLookAheadPrm      gLookAheadPrm[MAX_CRD];
    TCrdData           lockData[200];
    TPci               gPci;
} TCardData;

#pragma pack()

#endif // CARDDATA_H
