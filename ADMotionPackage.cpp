#include "ADMotionPackage.h"

#include "card.h"
#include "ioctrl.h"
#include "CardData.h"
#include "NetPackageRead.h"
#include "ThreadMsgType.h"

#ifdef _WIN32
#include <windows.h>
#else
#include <string.h>
#endif

namespace SYSTEMOPT
{
inline unsigned __int64 GetTickCount()
{
#ifdef _WIN32
    return ::GetTickCount();
#else
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (ts.tv_sec * 1000 + ts.tv_nsec / 1000000);
#endif
}
} // namespace SYSTEMOPT

int TPackage_Motion::FindHead(const unsigned char* buff, int len, HEAD_Motion& Head)
{
    const unsigned char* p = buff;
    int LoopLen = len - this->HeadSize + 1 ;
    for (int i = 0; i < LoopLen; i++, ++p)
    {
        if (p[0] == '@' && p[1] == '@') // orderflag 指令反馈
        {
            //反馈一个 gPci 结构
            Head.type = ADMotionDataType::ADMC_CMD;
            if (len - i >= 10)
            {
                unsigned short* pShort = (unsigned short*)(p + 2);
                Head.flag         = pShort[0];
                Head.ArrayCount      = pShort[1];//short数组个数
                Head.checksum     = pShort[2];
                Head.tick         = pShort[3];   //Command索引
                if (Head.ArrayCount > COMMAND_DATA_LEN)
                    return -1;
                Head.datalen = Head.ArrayCount * sizeof(short) + 8; // + TPci头
                return i;
            }
            else
            {
                Head.datalen = 10 ; //已找到包头，要求更多的数据，才能继续解析是否一个完整的包。
                return i;
                //return -1;
            }
        }
        else if (p[0] == '$' && p[1] == '$') // ioflag IO端口反馈（含轴IO）
        {
            Head.type    = ADMotionDataType::ADMC_IO;
            if(len-i > 5)
            {
                Head.StartIndex_IOData = 4;
                Head.AxisIO_num = p[2];  //轴数
                Head.IO_inputNum = p[3]; //I口组数量 每组16个
                Head.IO_outputNum = p[4];//O口组数量 每组16个
                Head.IOChange = 0;//保留 p[5]
                Head.datalen = Head.AxisIO_num*2+Head.IO_inputNum*2+Head.IO_outputNum*2+Head.StartIndex_IOData;
            }
            else
            {
                Head.datalen = 4 ;
            }
            return i;
        }
        else if (p[0] == '%' && p[1] == '%') // posflag 运控轴位置反馈
        {
            if(len-i > 2)
            {
                Head.type    = ADMotionDataType::ADMC_POS;
                Head.Axis_num = p[2];//轴数
                Head.AxisPosType = p[3];//轴位置数据类型 0=32 位整数  1=64 位整数 2=32 位浮点数 3=64 位浮点数
                Head.StartIndex_AxisPos = 2 ;
                switch (Head.AxisPosType)
                {
                case 0:
                    Head.datalen = Head.Axis_num*4+2;
                    break;
                case 1:
                    Head.datalen = Head.Axis_num*8+2;
                    break;
                case 2:
                    Head.datalen = Head.Axis_num*4+2;
                    break;
                case 3:
                    Head.datalen = Head.Axis_num*8+2;
                    break;
                }
            }
            else
              Head.datalen = 2 ;  //再需要2字节数据才能解释
            return i;
        }
        else if (p[0] == '#' && p[1] == '#')
        {
            Head.type = ADMotionDataType::ADMC_PDO;
            Head.datalen = 10;
            return i;
        }
    }
    return -1;
}
void TPackage_Motion::ReadPackage(HEAD_Motion const& Head, std::string& data, void* CAllTag)
{
    TADMotionConn* pADMotion = (TADMotionConn*)CAllTag;
    if (pADMotion == nullptr)
        return;
    TCardData*  p        = pADMotion->CardData;
    const char* tempbuff = data.data();
    std::string b16Str;
    // 使用枚举类型进行switch
    switch (Head.type)
    {
        case ADMotionDataType::ADMC_CMD://@@ 运控回复上位机的指令
            p->gPci.flag       = Head.flag;
            p->gPci.ArrayCount = Head.ArrayCount; // 2
            p->gPci.checksum   = Head.checksum;
            p->gPci.tick       = Head.tick;
            memcpy(p->gPci.data, tempbuff + 8, Head.ArrayCount * sizeof(short));
            SemPost(&p->pctrl->sema);
            break;
        case ADMotionDataType::ADMC_IO: //$$ 运控主动上传IO，含轴IO及轴状态
          {
            uint16_t tmpInput[4];
            uint16_t tmpOutput[4];
            bool IChange = false ;
            bool OChange = false ;
            bool AChange = false ;
            TCardIO NewCardIO;
            TCardIO OldCardIO;
            memset(&NewCardIO,0,sizeof(TCardIO));
            const char*piodata  = tempbuff + Head.StartIndex_IOData;
            for (int i = 0; i < Head.AxisIO_num / 2; i++)
            {
                NewCardIO.positionCrd[i][0] = *(short*)(piodata);
                piodata += 2;
                NewCardIO.positionCrd[i][1] = *(short*)(piodata);
                piodata += 2;
                if(p->gCardIO.positionCrd[i][0] != NewCardIO.positionCrd[i][0] ||
                   p->gCardIO.positionCrd[i][1] != NewCardIO.positionCrd[i][1] )
                    AChange = true ;
            }
            for(int i = 0; i < Head.IO_inputNum; i++)
            {
                NewCardIO.Input[i]= *(short*)(piodata);
                piodata += 2;
                if(p->gCardIO.Input[i] != NewCardIO.Input[i])
                    IChange = true ;
            }
            for (int i = 0; i < Head.IO_outputNum; i++)
            {
                NewCardIO.Output[i]= *(short*)(piodata);
                piodata += 2;
                if(p->gCardIO.Output[i] != NewCardIO.Output[i])
                    OChange = true ;
            }
            if(IChange || OChange || AChange)
            {
                std::unique_lock<std::shared_mutex> lock(p->pctrl->cardio_mutex);
                OldCardIO =  p->gCardIO;
                p->gCardIO = NewCardIO ;
            }
            p->tick_UpdategCardIO = SYSTEMOPT::GetTickCount();
            if (pADMotion->callback_data)
                pADMotion->callback_data(pADMotion,Head,data,pADMotion->Event_Tag);
            if(pADMotion->OnIOChange && (IChange || OChange || AChange))
            {
                unsigned char *flag = (unsigned char *)&Head.IOChange;
                *flag = 0 ;
                if(AChange) *flag += 0x01;
                if(IChange) *flag += 0x02;
                if(OChange) *flag += 0x04;
                pADMotion->OnIOChange(pADMotion,Head,OldCardIO,NewCardIO,pADMotion->Event_Tag);
            }
        }
        break;
        case ADMotionDataType::ADMC_POS://%% 运控主动上传各轴坐标，

        memcpy(&p->gCardAxisPos, tempbuff + Head.StartIndex_AxisPos, Head.Axis_num *4 );//sizeof(TCardAxisPos)
        if (pADMotion->callback_data)
        {
            pADMotion->callback_data(pADMotion,Head,data,pADMotion->Event_Tag);
        }
            break;
        case ADMotionDataType::ADMC_PDO://## 运控主动上传PDO数据
            if (pADMotion->callback_data)
                pADMotion->callback_data(pADMotion,Head,data,pADMotion->Event_Tag);
            break;
        default:
            // ThreadPostMessage();
            break;
    }
}
//---------------------------------------------------------------------------------------------
void InitCardData(TCardData *pData)
{
  memset(pData, 0, sizeof(TCardData));//这块内存不能写
}

TADMotionConn::TADMotionConn()
{
    m_ip                     = "***********";
    m_port                   = 6666;
    CardNo = 0 ;
    PackageReader.Pack.Title = "ADMotionConn";
    CardData = new TCardData;
    InitCardData(CardData);
    CardData->pctrl = new TCardData_cplusplus;
    SemInit(&CardData->pctrl->sema);
    PackageReader.Pack.pOnReadData     = PackageReader.Pack.ReadPackage;
    PackageReader.Pack.TagCall         = this;
    m_socket = new TAsioSocket_Queue; //使用队列
    m_socket->SetCallback(PackageReader);
}
TADMotionConn::~TADMotionConn()
{
    Connected = false;
    if(f_pConnected)
        *f_pConnected = Connected ;
    if (m_socket)
    {
        if (m_socket->isConnected())
            m_socket->close();
        delete m_socket;
        m_socket = nullptr;
    }
    SemDestroy(&CardData->pctrl->sema);
    if (CardData)
    {
        delete CardData->pctrl ;
        delete CardData;
        CardData = nullptr;
    }
}
bool TADMotionConn::Connection(std::string const& ipaddress, int port)
{
    Connecting = true;
    if(f_pConnecting)
        *f_pConnecting = Connecting ;
    m_ip       = ipaddress;
    m_port     = port;
    PackageReader.Cancel();
    if (!m_socket)
    {
        m_socket = new TAsioSocket_Queue; //使用队列
        m_socket->SetCallback(PackageReader);
    }
    SemReset(&CardData->pctrl->sema);
    int  ret = m_socket->connectHost(ipaddress, port);
    if (ret == 0)
    {
        PackageReader.Pack.ipstr = ipaddress + ":" + std::to_string(port);
        // m_socket->setCallback(PackageReader.OnReadSocket, &PackageReader);
        //  m_socket->setCallback(Event);//事件通知,断线，错误等，内部已处理，外部需要时可回调通知，也可从消息中截取。
        Connected  = true;
        Connecting = false;
        m_socket->async_read();
        if(f_pConnected)
            *f_pConnected = Connected ;
        if(f_pConnecting)
            *f_pConnecting = Connecting ;
        m_socket->DoSocketEvent(EVENT_AfterConnected);
        return true;
    }
    else
    {
        Connecting = false;
        if(f_pConnecting)
            *f_pConnecting = Connecting ;
        return false;
    }
}
unsigned __int64 TADMotionConn::GetTick_UpdategCardIO() { return CardData->tick_UpdategCardIO; }
void             TADMotionConn::Disconnect()
{
    Close();
    if (m_socket)
        m_socket->Client_Disconnect();
}
void TADMotionConn::SendCloseMsg()
{
    if(m_socket && m_socket->isConnected())
    {
        std::lock_guard< std::mutex > Lock(CardData->pctrl->socketmutex);
        char buff[1] = { '#' };
        m_socket->write(buff, 1);
    }
}
void TADMotionConn::Close()
{
    Connected = false;
    if(f_pConnected)
        *f_pConnected = Connected ;
    if (m_socket)
    {
        if (m_socket->isConnected())
        {
            //char buff[1] = { '#' };
            //m_socket->write(buff, 1);
            m_socket->close();
        }
    }
    PackageReader.Cancel();
}

